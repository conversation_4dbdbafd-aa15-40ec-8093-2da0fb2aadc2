'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Search, Download, ExternalLink, Calendar, Users, CheckCircle, Eye } from 'lucide-react'
import Image from 'next/image'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MinimalProgress } from '@/components/ui/minimal-progress'
import { PaperViewer } from '@/components/ui/paper-viewer'
import { SourceFilter } from '@/components/features/search/source-filter'
import { ClientSearchInput } from '@/components/features/search/client-search-input'
import { EnhancedSearchResults } from '@/components/features/search/enhanced-search-results'
import { SearchResultsHeader } from '@/components/features/search/search-results-header'
import { useSearch } from '@/lib/hooks/useSearch'
import { useDownloads } from '@/lib/hooks/useDownloads'
import { PaperDTO, PaperSource, DownloadStatus } from '@/lib/types'
import { formatDate } from '@/lib/utils'

export default function SearchPage() {
  const router = useRouter()
  const {
    isSearching,
    searchResults,
    searchError,
    performSearch,
    hasResults,
    getTotalResults,
    searchHistory,
    removeFromHistory,
    clearSearchHistory,
    preferredSources,
    updatePreferredSources
  } = useSearch()
  
  const { 
    createDownloadTask, 
    downloadSinglePaper, 
    isCreatingTask, 
    activeDownloads,
    totalActiveDownloads,
    globalProgress,
    downloadTasks,
    singleDownloads
  } = useDownloads()
  
  const [selectedPapers, setSelectedPapers] = useState<Set<string>>(new Set())
  const [downloadingPapers, setDownloadingPapers] = useState<Set<string>>(new Set())
  const [selectedSources, setSelectedSources] = useState<PaperSource[]>([
    PaperSource.ARXIV,
    PaperSource.CROSSREF,
    PaperSource.ASM
  ])
  const [searchQuery, setSearchQuery] = useState('')
  const [viewerState, setViewerState] = useState<{
    isOpen: boolean
    pdfUrl: string
    paperTitle: string
    paperUrl?: string
  }>({
    isOpen: false,
    pdfUrl: '',
    paperTitle: '',
    paperUrl: ''
  })

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    if (selectedSources.length === 0) {
      console.warn('No sources selected for search')
      return
    }

    try {
      await performSearch({
        query: searchQuery,
        sources: selectedSources,
        maxResults: 20,
      })
    } catch (error) {
      console.error('Search failed:', error)
      // Error is already handled in useSearch hook
    }
  }

  const handleSourceChange = (sources: PaperSource[]) => {
    setSelectedSources(sources)
    updatePreferredSources(sources)
  }

  const handleHistorySelect = (query: string, sources: PaperSource[]) => {
    setSearchQuery(query)
    setSelectedSources(sources)
    updatePreferredSources(sources)
  }

  // Sync selected sources with preferred sources after hydration
  useEffect(() => {
    if (preferredSources.length > 0) {
      setSelectedSources(preferredSources)
    }
  }, [preferredSources])

  const handlePaperSelect = (paperId: string) => {
    const newSelected = new Set(selectedPapers)
    if (newSelected.has(paperId)) {
      newSelected.delete(paperId)
    } else {
      newSelected.add(paperId)
    }
    setSelectedPapers(newSelected)
  }

  const handleSelectAll = () => {
    if (!searchResults?.papers) return
    const allPaperIds = new Set(searchResults.papers.map(paper => paper.id))
    setSelectedPapers(allPaperIds)
  }

  const handleClearSelection = () => {
    setSelectedPapers(new Set())
  }

  const handleBulkDownload = async () => {
    if (selectedPapers.size === 0) return
    
    const paperIds = Array.from(selectedPapers)
    const selectedPapersData = searchResults?.papers.filter(paper => selectedPapers.has(paper.id)) || []
    await createDownloadTask(paperIds, selectedPapersData)
    setSelectedPapers(new Set()) // Clear selection after download starts
  }

  const handleSingleDownload = async (paper: PaperDTO) => {
    setDownloadingPapers(prev => new Set(prev).add(paper.id))
    try {
      await downloadSinglePaper(paper)
    } catch (error) {
      console.error('Failed to download paper:', error)
    } finally {
      setDownloadingPapers(prev => {
        const newSet = new Set(prev)
        newSet.delete(paper.id)
        return newSet
      })
    }
  }

  const handleViewPaper = (paper: PaperDTO) => {
    if (paper.pdf_url) {
      setViewerState({
        isOpen: true,
        pdfUrl: paper.pdf_url,
        paperTitle: paper.title,
        paperUrl: paper.url
      })
    }
  }

  const handleShowDownloads = () => {
    router.push('/downloads')
  }

  // Check if paper is downloaded
  const isPaperDownloaded = (paperId: string): boolean => {
    // Check in completed download tasks containing this paper
    const isInTasks = downloadTasks.some(task =>
      task.status === DownloadStatus.COMPLETED &&
      task.download_urls &&
      task.download_urls.length > 0 &&
      task.paper_ids.includes(paperId)
    )
    
    // Check in single downloads
    const isInSingleDownloads = singleDownloads.some(download =>
      download.paper_id === paperId &&
      download.status === DownloadStatus.COMPLETED
    )
    
    return isInTasks || isInSingleDownloads
  }



  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Search Research Papers</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Search across multiple academic databases including arXiv, Crossref, Google Scholar, IEEE, and more.
          Download papers individually or in bulk with real-time progress tracking.
        </p>
      </div>

      {/* Search Form */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <ClientSearchInput
                value={searchQuery}
                onChange={setSearchQuery}
                onSubmit={handleSearch}
                disabled={isSearching}
                searchHistory={searchHistory}
                onSelectFromHistory={handleHistorySelect}
                onRemoveFromHistory={removeFromHistory}
                onClearHistory={clearSearchHistory}
                autoFocus={true}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching || selectedSources.length === 0 || !searchQuery.trim()}
              size="lg"
              className="h-12 px-8"
              title={selectedSources.length === 0 ? "Please select at least one source" : "Search"}
            >
              {isSearching ? (
                <div className="spinner mr-2" />
              ) : (
                <Search className="w-5 h-5 mr-2" />
              )}
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Source Filter */}
      <SourceFilter
        selectedSources={selectedSources}
        onSourceChange={handleSourceChange}
        disabled={isSearching}
      />

      {/* Results Header */}
      {hasResults() && (
        <SearchResultsHeader
          totalResults={getTotalResults()}
          selectedCount={selectedPapers.size}
          searchTime={searchResults?.search_time || 0}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
          onBulkDownload={handleBulkDownload}
          isCreatingTask={isCreatingTask}
          totalActiveDownloads={totalActiveDownloads}
          onShowDownloads={handleShowDownloads}
        />
      )}

      {/* Search Error */}
      {searchError && (
        <Card className="border-destructive">
          <CardContent className="p-4">
            <p className="text-destructive">Error: {searchError}</p>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {searchResults && (
        <EnhancedSearchResults
          papers={searchResults.papers}
          selectedPapers={selectedPapers}
          downloadingPapers={downloadingPapers}
          onPaperSelect={handlePaperSelect}
          onSingleDownload={handleSingleDownload}
          onViewPaper={handleViewPaper}
          isPaperDownloaded={isPaperDownloaded}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
          onBulkDownload={handleBulkDownload}
        />
      )}

      {/* Minimal Progress Bar - Fixed position at bottom right */}
      <MinimalProgress
        totalActiveDownloads={totalActiveDownloads}
        globalProgress={globalProgress}
        activeDownloads={activeDownloads}
        onShowDetails={handleShowDownloads}
      />

      {/* Paper Viewer */}
      {viewerState.isOpen && (
        <PaperViewer
          isOpen={viewerState.isOpen}
          onClose={() => setViewerState({ ...viewerState, isOpen: false })}
          pdfUrl={viewerState.pdfUrl}
          paperTitle={viewerState.paperTitle}
          paperUrl={viewerState.paperUrl}
        />
      )}
    </div>
  )
}

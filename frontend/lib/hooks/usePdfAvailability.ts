/**
 * React hook for PDF availability checking
 * Provides reactive state management for PDF availability with MVVM pattern
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { PaperDTO } from '../types'
import { pdfAvailabilityService, PdfAvailabilityStatus, PdfAvailabilityResult } from '../services/pdf-availability'

interface PdfAvailabilityState {
  [paperId: string]: {
    status: PdfAvailabilityStatus
    pdf_url?: string
    lastChecked: number
  }
}

interface UsePdfAvailabilityReturn {
  // State
  availabilityState: PdfAvailabilityState
  isChecking: boolean
  
  // Actions
  checkAvailability: (paper: PaperDTO) => Promise<void>
  checkBatchAvailability: (papers: PaperDTO[]) => Promise<void>
  getPdfUrl: (paper: PaperDTO) => Promise<string | null>
  
  // Utilities
  getStatus: (paperId: string) => PdfAvailabilityStatus
  isAvailable: (paperId: string) => boolean
  isUnavailable: (paperId: string) => boolean
  hasError: (paperId: string) => boolean
  clearCache: () => void
  invalidateCache: (paper: PaperDTO) => void
}

export const usePdfAvailability = (): UsePdfAvailabilityReturn => {
  const [availabilityState, setAvailabilityState] = useState<PdfAvailabilityState>({})
  const [isChecking, setIsChecking] = useState(false)
  const checkingCountRef = useRef(0)

  // Update checking state based on active checks
  const updateCheckingState = useCallback(() => {
    setIsChecking(checkingCountRef.current > 0)
  }, [])

  // Check availability for a single paper
  const checkAvailability = useCallback(async (paper: PaperDTO) => {
    // Set checking status immediately
    setAvailabilityState(prev => ({
      ...prev,
      [paper.id]: {
        status: PdfAvailabilityStatus.CHECKING,
        lastChecked: Date.now()
      }
    }))

    checkingCountRef.current++
    updateCheckingState()

    try {
      const result = await pdfAvailabilityService.checkAvailability(paper)
      
      setAvailabilityState(prev => ({
        ...prev,
        [paper.id]: {
          status: result.status,
          pdf_url: result.pdf_url,
          lastChecked: Date.now()
        }
      }))
    } catch (error) {
      console.error('PDF availability check failed:', error)
      
      setAvailabilityState(prev => ({
        ...prev,
        [paper.id]: {
          status: PdfAvailabilityStatus.ERROR,
          lastChecked: Date.now()
        }
      }))
    } finally {
      checkingCountRef.current--
      updateCheckingState()
    }
  }, [updateCheckingState])

  // Check availability for multiple papers
  const checkBatchAvailability = useCallback(async (papers: PaperDTO[]) => {
    // Set checking status for all papers
    setAvailabilityState(prev => {
      const newState = { ...prev }
      papers.forEach(paper => {
        newState[paper.id] = {
          status: PdfAvailabilityStatus.CHECKING,
          lastChecked: Date.now()
        }
      })
      return newState
    })

    checkingCountRef.current++
    updateCheckingState()

    try {
      const results = await pdfAvailabilityService.checkBatchAvailability(papers)
      
      setAvailabilityState(prev => {
        const newState = { ...prev }
        Object.entries(results).forEach(([paperId, result]) => {
          newState[paperId] = {
            status: result.status,
            pdf_url: result.pdf_url,
            lastChecked: Date.now()
          }
        })
        return newState
      })
    } catch (error) {
      console.error('Batch PDF availability check failed:', error)
      
      // Set error status for all papers
      setAvailabilityState(prev => {
        const newState = { ...prev }
        papers.forEach(paper => {
          newState[paper.id] = {
            status: PdfAvailabilityStatus.ERROR,
            lastChecked: Date.now()
          }
        })
        return newState
      })
    } finally {
      checkingCountRef.current--
      updateCheckingState()
    }
  }, [updateCheckingState])

  // Get PDF URL if available
  const getPdfUrl = useCallback(async (paper: PaperDTO): Promise<string | null> => {
    const currentState = availabilityState[paper.id]
    
    // If we already know it's available, return the URL
    if (currentState?.status === PdfAvailabilityStatus.AVAILABLE && currentState.pdf_url) {
      return currentState.pdf_url
    }
    
    // If we haven't checked or it's been a while, check again
    if (!currentState || Date.now() - currentState.lastChecked > 5 * 60 * 1000) {
      await checkAvailability(paper)
    }
    
    // Return URL from service
    return pdfAvailabilityService.getPdfUrl(paper)
  }, [availabilityState, checkAvailability])

  // Utility functions
  const getStatus = useCallback((paperId: string): PdfAvailabilityStatus => {
    return availabilityState[paperId]?.status || PdfAvailabilityStatus.UNKNOWN
  }, [availabilityState])

  const isAvailable = useCallback((paperId: string): boolean => {
    return getStatus(paperId) === PdfAvailabilityStatus.AVAILABLE
  }, [getStatus])

  const isUnavailable = useCallback((paperId: string): boolean => {
    return getStatus(paperId) === PdfAvailabilityStatus.UNAVAILABLE
  }, [getStatus])

  const hasError = useCallback((paperId: string): boolean => {
    return getStatus(paperId) === PdfAvailabilityStatus.ERROR
  }, [getStatus])

  const clearCache = useCallback(() => {
    pdfAvailabilityService.clearCache()
    setAvailabilityState({})
  }, [])

  const invalidateCache = useCallback((paper: PaperDTO) => {
    pdfAvailabilityService.invalidateCache(paper)
    setAvailabilityState(prev => {
      const newState = { ...prev }
      delete newState[paper.id]
      return newState
    })
  }, [])

  return {
    // State
    availabilityState,
    isChecking,
    
    // Actions
    checkAvailability,
    checkBatchAvailability,
    getPdfUrl,
    
    // Utilities
    getStatus,
    isAvailable,
    isUnavailable,
    hasError,
    clearCache,
    invalidateCache
  }
}

// Hook for checking PDF availability on search results
export const useSearchResultsPdfAvailability = (papers: PaperDTO[]) => {
  const pdfAvailability = usePdfAvailability()
  const [hasInitialCheck, setHasInitialCheck] = useState(false)

  // Automatically check PDF availability when papers change
  useEffect(() => {
    if (papers.length > 0 && !hasInitialCheck) {
      // Delay initial check to allow UI to render first
      const timer = setTimeout(() => {
        pdfAvailability.checkBatchAvailability(papers)
        setHasInitialCheck(true)
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [papers, hasInitialCheck, pdfAvailability])

  // Reset when papers change significantly
  useEffect(() => {
    setHasInitialCheck(false)
  }, [papers.length])

  return pdfAvailability
}

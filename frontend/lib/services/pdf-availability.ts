/**
 * PDF Availability Service
 * Handles asynchronous PDF availability checking with proper separation of concerns.
 */

import { PaperDTO } from '../types'

export enum PdfAvailabilityStatus {
  UNKNOWN = 'unknown',
  CHECKING = 'checking', 
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  ERROR = 'error'
}

export interface PdfAvailabilityResult {
  paper_id: string
  status: PdfAvailabilityStatus
  pdf_url?: string
}

export interface BatchPdfAvailabilityResult {
  results: Record<string, {
    status: PdfAvailabilityStatus
    pdf_url?: string
  }>
}

class UltraFastPdfAvailabilityService {
  private baseUrl: string
  private cache: Map<string, { status: PdfAvailabilityStatus; timestamp: number; pdf_url?: string }>
  private cacheTimeout: number = 3 * 60 * 1000 // 3 minutes for faster updates
  private pendingChecks: Map<string, Promise<PdfAvailabilityResult>>
  private batchQueue: Map<string, PaperDTO>
  private batchTimeout: NodeJS.Timeout | null = null
  private batchDelay: number = 100 // Ultra-fast 100ms batch delay
  private maxBatchSize: number = 50 // Large batch size for efficiency
  private maxConcurrentRequests: number = 10 // High concurrency

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'
    this.cache = new Map()
    this.pendingChecks = new Map()
    this.batchQueue = new Map()

    // Periodic cache cleanup for performance
    setInterval(() => this.cleanupCache(), 60000) // Every minute
  }

  /**
   * Ultra-fast PDF availability check with intelligent batching
   */
  async checkAvailability(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    const cacheKey = this.getCacheKeyFast(paper)

    // Ultra-fast cache lookup
    const cached = this.getCachedResultFast(cacheKey)
    if (cached) {
      return {
        paper_id: paper.id,
        status: cached.status,
        pdf_url: cached.pdf_url
      }
    }

    // Check if already pending
    const pending = this.pendingChecks.get(cacheKey)
    if (pending) {
      return pending
    }

    // Fast path: if paper has PDF URL, check immediately
    if (paper.pdf_url) {
      const promise = this.createFastCheckPromise(paper)
      this.pendingChecks.set(cacheKey, promise)

      try {
        const result = await promise
        this.cacheResultFast(cacheKey, result.status, result.pdf_url)
        return result
      } finally {
        this.pendingChecks.delete(cacheKey)
      }
    }

    // Add to batch queue for papers without PDF URLs
    this.batchQueue.set(paper.id, paper)
    this.scheduleBatchCheckFast()

    // Create and cache the promise
    const promise = this.createSingleCheckPromise(paper)
    this.pendingChecks.set(cacheKey, promise)

    try {
      const result = await promise
      this.cacheResultFast(cacheKey, result.status, result.pdf_url)
      return result
    } finally {
      this.pendingChecks.delete(cacheKey)
    }
  }

  /**
   * Ultra-fast batch PDF availability checking with intelligent processing
   */
  async checkBatchAvailability(papers: PaperDTO[]): Promise<Record<string, PdfAvailabilityResult>> {
    const startTime = performance.now()
    const results: Record<string, PdfAvailabilityResult> = {}
    const papersToCheck: PaperDTO[] = []

    // Ultra-fast cache lookup
    for (const paper of papers) {
      const cacheKey = this.getCacheKeyFast(paper)
      const cached = this.getCachedResultFast(cacheKey)

      if (cached) {
        results[paper.id] = {
          paper_id: paper.id,
          status: cached.status,
          pdf_url: cached.pdf_url
        }
      } else {
        papersToCheck.push(paper)
      }
    }

    if (papersToCheck.length === 0) {
      return results
    }

    // Split papers into those with and without PDF URLs for optimized processing
    const papersWithPdf = papersToCheck.filter(p => p.pdf_url)
    const papersWithoutPdf = papersToCheck.filter(p => !p.pdf_url)

    try {
      // Process papers with PDF URLs using fast HEAD requests
      const pdfPromises = papersWithPdf.map(paper => this.fastPdfCheck(paper))

      // Process papers without PDF URLs using batch API
      const batchPromise = papersWithoutPdf.length > 0
        ? this.performBatchCheckFast(papersWithoutPdf)
        : Promise.resolve({ results: {} })

      // Wait for both to complete
      const [pdfResults, batchResults] = await Promise.all([
        Promise.allSettled(pdfPromises),
        batchPromise
      ])

      // Process PDF check results
      pdfResults.forEach((result, index) => {
        const paper = papersWithPdf[index]
        if (result.status === 'fulfilled') {
          results[paper.id] = result.value
          const cacheKey = this.getCacheKeyFast(paper)
          this.cacheResultFast(cacheKey, result.value.status, result.value.pdf_url)
        } else {
          results[paper.id] = {
            paper_id: paper.id,
            status: PdfAvailabilityStatus.ERROR
          }
        }
      })

      // Process batch API results
      for (const paper of papersWithoutPdf) {
        const result = batchResults.results[paper.id]
        if (result) {
          const cacheKey = this.getCacheKeyFast(paper)
          this.cacheResultFast(cacheKey, result.status, result.pdf_url)

          results[paper.id] = {
            paper_id: paper.id,
            status: result.status,
            pdf_url: result.pdf_url
          }
        } else {
          results[paper.id] = {
            paper_id: paper.id,
            status: PdfAvailabilityStatus.ERROR
          }
        }
      }

    } catch (error) {
      console.error('Batch PDF availability check failed:', error)

      // Set error status for all unchecked papers
      for (const paper of papersToCheck) {
        results[paper.id] = {
          paper_id: paper.id,
          status: PdfAvailabilityStatus.ERROR
        }
      }
    }

    const elapsed = performance.now() - startTime
    console.log(`Batch PDF check completed in ${elapsed.toFixed(2)}ms for ${papers.length} papers`)

    return results
  }

  /**
   * Get PDF URL if available
   */
  async getPdfUrl(paper: PaperDTO): Promise<string | null> {
    const result = await this.checkAvailability(paper)
    return result.status === PdfAvailabilityStatus.AVAILABLE ? result.pdf_url || null : null
  }

  /**
   * Clear cache for a specific paper
   */
  invalidateCache(paper: PaperDTO): void {
    const cacheKey = this.getCacheKey(paper)
    this.cache.delete(cacheKey)
  }

  /**
   * Clear all cached results
   */
  clearCache(): void {
    this.cache.clear()
  }

  private getCacheKeyFast(paper: PaperDTO): string {
    // Ultra-fast cache key generation
    return paper.doi || `t:${paper.title.substring(0, 30)}`
  }

  private getCachedResultFast(cacheKey: string): { status: PdfAvailabilityStatus; pdf_url?: string } | null {
    const cached = this.cache.get(cacheKey)
    if (!cached) return null

    // Fast timestamp check
    const now = Date.now()
    if (now - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(cacheKey)
      return null
    }

    return { status: cached.status, pdf_url: cached.pdf_url }
  }

  private cacheResultFast(cacheKey: string, status: PdfAvailabilityStatus, pdf_url?: string): void {
    this.cache.set(cacheKey, {
      status,
      pdf_url,
      timestamp: Date.now()
    })
  }

  private cleanupCache(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  private async fastPdfCheck(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout

      const response = await fetch(paper.pdf_url!, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      })

      clearTimeout(timeoutId)

      const isAvailable = response.ok &&
        response.headers.get('content-type')?.includes('application/pdf')

      return {
        paper_id: paper.id,
        status: isAvailable ? PdfAvailabilityStatus.AVAILABLE : PdfAvailabilityStatus.UNAVAILABLE,
        pdf_url: isAvailable ? paper.pdf_url : undefined
      }
    } catch (error) {
      return {
        paper_id: paper.id,
        status: PdfAvailabilityStatus.ERROR
      }
    }
  }

  private async createFastCheckPromise(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    return this.fastPdfCheck(paper)
  }

  private scheduleBatchCheckFast(): void {
    if (this.batchTimeout) return

    this.batchTimeout = setTimeout(() => {
      this.processBatchQueueFast()
      this.batchTimeout = null
    }, this.batchDelay)
  }

  private async processBatchQueueFast(): Promise<void> {
    if (this.batchQueue.size === 0) return

    const papers = Array.from(this.batchQueue.values())
    this.batchQueue.clear()

    // Process in chunks if batch is too large
    const chunks = this.chunkArray(papers, this.maxBatchSize)

    try {
      const chunkPromises = chunks.map(chunk => this.performBatchCheckFast(chunk))
      await Promise.allSettled(chunkPromises)
    } catch (error) {
      console.error('Batch queue processing failed:', error)
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  private async createSingleCheckPromise(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    try {
      const response = await fetch(`${this.baseUrl}/papers/pdf-availability/check/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paper: {
            id: paper.id,
            title: paper.title,
            doi: paper.doi,
            pdf_url: paper.pdf_url,
            url: paper.url,
            source: paper.source
          }
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      return {
        paper_id: paper.id,
        status: result.status as PdfAvailabilityStatus,
        pdf_url: result.pdf_url
      }
    } catch (error) {
      console.error('Single PDF availability check failed:', error)
      return {
        paper_id: paper.id,
        status: PdfAvailabilityStatus.ERROR
      }
    }
  }

  private async performBatchCheckFast(papers: PaperDTO[]): Promise<BatchPdfAvailabilityResult> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    try {
      const response = await fetch(`${this.baseUrl}/papers/pdf-availability/batch/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          papers: papers.map(paper => ({
            id: paper.id,
            title: paper.title,
            doi: paper.doi,
            pdf_url: paper.pdf_url,
            url: paper.url,
            source: paper.source
          }))
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      return response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }
}

// Export singleton instance of ultra-fast service
export const pdfAvailabilityService = new UltraFastPdfAvailabilityService()

#!/usr/bin/env python3
"""
Test script for ultra-fast search orchestrator and PDF availability optimizations.
"""

import os
import sys
import django
import asyncio
import time
from typing import List

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.search_orchestrator import search_orchestrator
from apps.papers.data.models import SearchRequestDTO, PaperSource
from apps.papers.services.pdf_availability import PdfAvailabilityService
from apps.papers.domain.models import Paper


async def test_ultra_fast_search():
    """Test the ultra-fast search orchestrator."""
    print("🚀 Testing Ultra-Fast Search Orchestrator")
    print("=" * 50)
    
    # Test query
    test_query = "machine learning neural networks"
    
    # Create search request
    request = SearchRequestDTO(
        query=test_query,
        max_results=20,
        sources=[PaperSource.ARXIV, PaperSource.CROSSREF]
    )
    
    print(f"Query: {test_query}")
    print(f"Max Results: {request.max_results}")
    print(f"Sources: {[s.value for s in request.sources]}")
    print()
    
    # Measure search performance
    start_time = time.perf_counter()
    
    try:
        papers = await search_orchestrator.search_papers(request)
        
        search_time = time.perf_counter() - start_time
        
        print(f"✅ Search completed successfully!")
        print(f"⏱️  Search time: {search_time:.3f} seconds")
        print(f"📄 Papers found: {len(papers)}")
        print()
        
        # Display sample results
        print("📋 Sample Results:")
        print("-" * 30)
        for i, paper in enumerate(papers[:5]):
            print(f"{i+1}. {paper.title[:80]}...")
            print(f"   Source: {paper.source}")
            print(f"   DOI: {paper.doi or 'N/A'}")
            print(f"   PDF URL: {'✅' if paper.pdf_url else '❌'}")
            print()
        
        return papers
        
    except Exception as e:
        search_time = time.perf_counter() - start_time
        print(f"❌ Search failed after {search_time:.3f} seconds")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return []


async def test_ultra_fast_pdf_availability(papers: List):
    """Test the ultra-fast PDF availability service."""
    print("🔍 Testing Ultra-Fast PDF Availability Service")
    print("=" * 50)
    
    if not papers:
        print("❌ No papers to test PDF availability")
        return
    
    # Convert DTOs to domain objects for testing
    domain_papers = []
    for paper_dto in papers[:10]:  # Test first 10 papers
        paper = Paper(
            title=paper_dto.title,
            doi=paper_dto.doi,
            url=paper_dto.url,
            pdf_url=paper_dto.pdf_url,
            source=paper_dto.source.value if paper_dto.source else None
        )
        domain_papers.append(paper)
    
    print(f"Testing PDF availability for {len(domain_papers)} papers")
    print()
    
    # Test batch availability checking
    pdf_service = PdfAvailabilityService()
    
    start_time = time.perf_counter()
    
    try:
        # Test batch checking
        results = await pdf_service.check_batch_availability(domain_papers)
        
        check_time = time.perf_counter() - start_time
        
        print(f"✅ PDF availability check completed!")
        print(f"⏱️  Check time: {check_time:.3f} seconds")
        print(f"📊 Results: {len(results)}")
        print()
        
        # Display results
        print("📋 PDF Availability Results:")
        print("-" * 35)
        
        available_count = 0
        unavailable_count = 0
        error_count = 0
        
        for i, paper in enumerate(domain_papers):
            paper_id = paper.doi or paper.title[:50]
            status = results.get(paper_id, 'unknown')
            status_value = status.value if hasattr(status, 'value') else str(status)
            
            if status_value == 'available':
                status_icon = "✅"
                available_count += 1
            elif status_value == 'unavailable':
                status_icon = "❌"
                unavailable_count += 1
            elif status_value == 'error':
                status_icon = "⚠️"
                error_count += 1
            else:
                status_icon = "❓"
            
            print(f"{i+1}. {status_icon} {paper.title[:60]}...")
            print(f"   Status: {status_value}")
            print(f"   Source: {paper.source}")
            print()
        
        print("📈 Summary:")
        print(f"   Available: {available_count}")
        print(f"   Unavailable: {unavailable_count}")
        print(f"   Errors: {error_count}")
        print()
        
    except Exception as e:
        check_time = time.perf_counter() - start_time
        print(f"❌ PDF availability check failed after {check_time:.3f} seconds")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


async def test_performance_comparison():
    """Test performance with different query sizes."""
    print("📊 Performance Comparison Test")
    print("=" * 50)
    
    test_queries = [
        ("Small query", "CRISPR", 10),
        ("Medium query", "machine learning deep learning", 25),
        ("Large query", "artificial intelligence neural networks", 50)
    ]
    
    for query_name, query, max_results in test_queries:
        print(f"Testing {query_name}: '{query}' (max {max_results} results)")
        
        request = SearchRequestDTO(
            query=query,
            max_results=max_results,
            sources=[PaperSource.ARXIV, PaperSource.CROSSREF]
        )
        
        start_time = time.perf_counter()
        
        try:
            papers = await search_orchestrator.search_papers(request)
            elapsed = time.perf_counter() - start_time
            
            print(f"  ✅ {len(papers)} papers in {elapsed:.3f}s")
            print(f"  📈 Rate: {len(papers)/elapsed:.1f} papers/second")
            
        except Exception as e:
            elapsed = time.perf_counter() - start_time
            print(f"  ❌ Failed after {elapsed:.3f}s: {e}")
        
        print()


async def main():
    """Run all tests."""
    print("🧪 Ultra-Fast Search & PDF Availability Test Suite")
    print("=" * 60)
    print()
    
    # Test 1: Ultra-fast search
    papers = await test_ultra_fast_search()
    
    print()
    
    # Test 2: Ultra-fast PDF availability
    await test_ultra_fast_pdf_availability(papers)
    
    print()
    
    # Test 3: Performance comparison
    await test_performance_comparison()
    
    print("🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())

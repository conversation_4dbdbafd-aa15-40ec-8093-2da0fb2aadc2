#!/usr/bin/env python3
"""
Simple test for ultra-fast search to verify it's working correctly.
"""

import os
import sys
import django
import asyncio
import time

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.search_orchestrator import search_orchestrator
from apps.papers.data.models import SearchRequestDTO, PaperSource


async def test_simple_search():
    """Test a simple search to verify the system works."""
    print("🔬 Simple Search Test")
    print("=" * 30)
    
    # Test with ArXiv only (should be faster)
    request = SearchRequestDTO(
        query="quantum computing",
        max_results=5,
        sources=[PaperSource.ARXIV]
    )
    
    print(f"Query: {request.query}")
    print(f"Source: ArXiv only")
    print(f"Max Results: {request.max_results}")
    print()
    
    start_time = time.perf_counter()
    
    try:
        papers = await search_orchestrator.search_papers(request)
        elapsed = time.perf_counter() - start_time
        
        print(f"✅ Found {len(papers)} papers in {elapsed:.3f} seconds")
        print(f"📈 Rate: {len(papers)/elapsed:.1f} papers/second")
        print()
        
        # Show results
        for i, paper in enumerate(papers):
            print(f"{i+1}. {paper.title}")
            print(f"   DOI: {paper.doi or 'N/A'}")
            print(f"   PDF: {'✅' if paper.pdf_url else '❌'}")
            print()
            
    except Exception as e:
        elapsed = time.perf_counter() - start_time
        print(f"❌ Search failed after {elapsed:.3f} seconds")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


async def test_crossref_search():
    """Test CrossRef search."""
    print("🔬 CrossRef Search Test")
    print("=" * 30)
    
    request = SearchRequestDTO(
        query="machine learning",
        max_results=5,
        sources=[PaperSource.CROSSREF]
    )
    
    print(f"Query: {request.query}")
    print(f"Source: CrossRef only")
    print(f"Max Results: {request.max_results}")
    print()
    
    start_time = time.perf_counter()
    
    try:
        papers = await search_orchestrator.search_papers(request)
        elapsed = time.perf_counter() - start_time
        
        print(f"✅ Found {len(papers)} papers in {elapsed:.3f} seconds")
        print(f"📈 Rate: {len(papers)/elapsed:.1f} papers/second")
        print()
        
        # Show results
        for i, paper in enumerate(papers):
            print(f"{i+1}. {paper.title[:60]}...")
            print(f"   DOI: {paper.doi or 'N/A'}")
            print(f"   PDF: {'✅' if paper.pdf_url else '❌'}")
            print()
            
    except Exception as e:
        elapsed = time.perf_counter() - start_time
        print(f"❌ Search failed after {elapsed:.3f} seconds")
        print(f"Error: {e}")


async def main():
    """Run simple tests."""
    print("🧪 Simple Search Tests")
    print("=" * 40)
    print()
    
    await test_simple_search()
    print()
    await test_crossref_search()
    
    print("🎉 Simple tests completed!")


if __name__ == "__main__":
    asyncio.run(main())

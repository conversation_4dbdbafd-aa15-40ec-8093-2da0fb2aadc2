"""
Ultra-High Performance PDF Availability Service.
Optimized for maximum speed with minimal overhead and intelligent batching.
"""

import asyncio
import aiohttp
import time
import hashlib
from typing import List, Dict, Optional, Set, Tuple
from collections import defaultdict

from ..domain.interfaces import IPdfAvailabilityService, PdfAvailabilityStatus
from ..domain.models import Paper
from ..domain.publishers.factory import PublisherStrategyFactory


class UltraFastPdfAvailabilityService(IPdfAvailabilityService):
    """Ultra-optimized PDF availability service with maximum algorithmic performance."""

    def __init__(self):
        # Aggressive performance settings
        self.batch_size = 50  # Large batches for efficiency
        self.max_concurrent = 20  # High concurrency
        self.check_timeout = 3  # Fast timeout for availability checks

        # In-memory cache for ultra-fast access
        self._availability_cache = {}
        self._cache_timestamps = {}
        self._cache_ttl = 1800  # 30 minutes

        # Pre-allocated data structures
        self._batch_queue = []
        self._processing_set = set()

        # Network optimization
        self.connector_limit = 100
        self.per_host_limit = 30
        
    async def check_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Ultra-fast single paper availability check with intelligent caching."""
        # Fast cache key generation
        cache_key = self._get_cache_key_fast(paper)

        # Ultra-fast cache lookup
        cached_result = self._get_cached_fast(cache_key)
        if cached_result:
            return cached_result

        # Fast availability check
        status = await self._check_paper_availability_fast(paper)

        # Fast cache storage
        self._cache_fast(cache_key, status)

        return status
    
    async def check_batch_availability(self, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Ultra-fast batch availability checking with intelligent grouping and parallel processing."""
        start_time = time.perf_counter()
        results = {}

        # Fast cache lookup for all papers
        uncached_papers = []
        for paper in papers:
            cache_key = self._get_cache_key_fast(paper)
            cached_result = self._get_cached_fast(cache_key)
            if cached_result:
                paper_id = paper.doi or paper.title[:50]
                results[paper_id] = cached_result
            else:
                uncached_papers.append(paper)

        if not uncached_papers:
            return results

        # Ultra-fast source grouping with pre-allocated dict
        source_groups = defaultdict(list)
        for paper in uncached_papers:
            source = paper.source or 'unknown'
            source_groups[source].append(paper)

        # Parallel processing with aggressive concurrency
        tasks = []
        for source, source_papers in source_groups.items():
            task = self._check_source_batch_fast(source, source_papers)
            tasks.append(task)

        # Fast result collection
        source_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Combine results with minimal overhead
        for result in source_results:
            if isinstance(result, dict):
                results.update(result)

        elapsed = time.perf_counter() - start_time
        print(f"Batch availability check completed in {elapsed:.3f}s for {len(papers)} papers")

        return results
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Ultra-fast PDF URL retrieval with minimal validation."""
        # Fast path: return existing URL if available
        if paper.pdf_url:
            return paper.pdf_url

        # Fast publisher strategy lookup
        try:
            strategy = PublisherStrategyFactory.create_strategy(paper.source or 'crossref')
            async with strategy:
                return await strategy.get_pdf_url(paper)
        except Exception:
            return None

    def _get_cache_key_fast(self, paper: Paper) -> str:
        """Ultra-fast cache key generation using hash."""
        if paper.doi:
            return f"pdf:{hashlib.md5(paper.doi.encode()).hexdigest()[:16]}"
        else:
            title_hash = hashlib.md5(paper.title[:100].encode()).hexdigest()[:16]
            return f"pdf:{title_hash}"

    def _get_cached_fast(self, cache_key: str) -> Optional[PdfAvailabilityStatus]:
        """Ultra-fast cache lookup with TTL check."""
        if cache_key not in self._availability_cache:
            return None

        # Fast TTL check
        timestamp = self._cache_timestamps.get(cache_key, 0)
        if time.time() - timestamp > self._cache_ttl:
            # Expired, remove from cache
            del self._availability_cache[cache_key]
            del self._cache_timestamps[cache_key]
            return None

        return self._availability_cache[cache_key]

    def _cache_fast(self, cache_key: str, status: PdfAvailabilityStatus) -> None:
        """Ultra-fast cache storage with automatic cleanup."""
        current_time = time.time()

        # Store result
        self._availability_cache[cache_key] = status
        self._cache_timestamps[cache_key] = current_time

        # Periodic cleanup to prevent memory bloat
        if len(self._availability_cache) > 10000:
            self._cleanup_cache_fast(current_time)

    def _cleanup_cache_fast(self, current_time: float) -> None:
        """Fast cache cleanup removing expired entries."""
        expired_keys = [
            key for key, timestamp in self._cache_timestamps.items()
            if current_time - timestamp > self._cache_ttl
        ]

        for key in expired_keys:
            del self._availability_cache[key]
            del self._cache_timestamps[key]
    
    async def _check_paper_availability_fast(self, paper: Paper) -> PdfAvailabilityStatus:
        """Ultra-fast single paper availability check."""
        # Fast path: if no PDF URL and no DOI, likely unavailable
        if not paper.pdf_url and not paper.doi:
            return PdfAvailabilityStatus.UNAVAILABLE

        # Fast path: if PDF URL exists, do quick HEAD request
        if paper.pdf_url:
            try:
                connector = aiohttp.TCPConnector(limit=self.connector_limit)
                timeout = aiohttp.ClientTimeout(total=self.check_timeout)

                async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                    async with session.head(paper.pdf_url) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '').lower()
                            if 'application/pdf' in content_type:
                                return PdfAvailabilityStatus.AVAILABLE
                        return PdfAvailabilityStatus.UNAVAILABLE
            except Exception:
                return PdfAvailabilityStatus.ERROR

        # Fallback to publisher strategy
        if paper.source:
            try:
                strategy = PublisherStrategyFactory.create_strategy(paper.source)
                async with strategy:
                    return await strategy.check_pdf_availability(paper)
            except Exception:
                return PdfAvailabilityStatus.ERROR

        return PdfAvailabilityStatus.UNAVAILABLE
    
    async def _check_source_batch_fast(self, source: str, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Ultra-fast batch checking for papers from the same source."""
        results = {}

        if not papers:
            return results

        # Fast path for papers with PDF URLs
        pdf_papers = [p for p in papers if p.pdf_url]
        non_pdf_papers = [p for p in papers if not p.pdf_url]

        # Ultra-fast PDF URL validation in parallel
        if pdf_papers:
            pdf_results = await self._batch_check_pdf_urls(pdf_papers)
            results.update(pdf_results)

        # Handle papers without PDF URLs
        if non_pdf_papers:
            if source == 'unknown':
                # Fast unknown source handling
                for paper in non_pdf_papers:
                    paper_id = paper.doi or paper.title[:50]
                    results[paper_id] = PdfAvailabilityStatus.UNAVAILABLE
            else:
                # Use publisher strategy for papers without PDF URLs
                try:
                    strategy = PublisherStrategyFactory.create_strategy(source)
                    async with strategy:
                        strategy_results = await self._batch_check_with_strategy(strategy, non_pdf_papers)
                        results.update(strategy_results)
                except Exception:
                    # Fast error handling
                    for paper in non_pdf_papers:
                        paper_id = paper.doi or paper.title[:50]
                        results[paper_id] = PdfAvailabilityStatus.ERROR

        return results

    async def _batch_check_pdf_urls(self, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Ultra-fast batch PDF URL validation."""
        results = {}

        # Ultra-aggressive connector settings
        connector = aiohttp.TCPConnector(
            limit=self.connector_limit,
            limit_per_host=self.per_host_limit,
            ttl_dns_cache=600,
            use_dns_cache=True
        )
        timeout = aiohttp.ClientTimeout(total=self.check_timeout)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Create semaphore for controlled concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def check_single_url(paper: Paper) -> Tuple[str, PdfAvailabilityStatus]:
                async with semaphore:
                    paper_id = paper.doi or paper.title[:50]
                    try:
                        async with session.head(paper.pdf_url) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower()
                                if 'application/pdf' in content_type:
                                    return paper_id, PdfAvailabilityStatus.AVAILABLE
                            return paper_id, PdfAvailabilityStatus.UNAVAILABLE
                    except Exception:
                        return paper_id, PdfAvailabilityStatus.ERROR

            # Execute all checks in parallel
            tasks = [check_single_url(paper) for paper in papers]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in batch_results:
                if isinstance(result, tuple):
                    paper_id, status = result
                    results[paper_id] = status
                    # Cache the result immediately
                    cache_key = f"pdf:{hashlib.md5(paper_id.encode()).hexdigest()[:16]}"
                    self._cache_fast(cache_key, status)

        return results

    async def _batch_check_with_strategy(self, strategy, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Fast batch checking using publisher strategy."""
        results = {}
        semaphore = asyncio.Semaphore(self.max_concurrent)

        async def check_single_paper(paper: Paper) -> Tuple[str, PdfAvailabilityStatus]:
            async with semaphore:
                paper_id = paper.doi or paper.title[:50]
                try:
                    status = await strategy.check_pdf_availability(paper)
                    return paper_id, status
                except Exception:
                    return paper_id, PdfAvailabilityStatus.ERROR

        tasks = [check_single_paper(paper) for paper in papers]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in batch_results:
            if isinstance(result, tuple):
                paper_id, status = result
                results[paper_id] = status

        return results


# Export the ultra-fast implementation as the main service
PdfAvailabilityService = UltraFastPdfAvailabilityService

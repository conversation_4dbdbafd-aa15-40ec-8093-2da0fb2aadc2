"""
Repository interfaces for the paper downloader domain.
These interfaces define contracts for data access and external services.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union
from enum import Enum

from .models import Paper, SearchQuery, DownloadTask


class PublisherInteractionType(Enum):
    """Types of publisher interaction patterns."""
    API = "api"
    XML_FEED = "xml_feed"
    WEB_SCRAPING = "web_scraping"
    HYBRID = "hybrid"


class PdfAvailabilityStatus(Enum):
    """PDF availability status."""
    UNKNOWN = "unknown"
    CHECKING = "checking"
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"


class IPaperRepository(ABC):
    """Interface for paper data operations."""

    @abstractmethod
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search for papers based on query."""
        pass

    @abstractmethod
    async def download_paper_pdf(self, paper: Paper) -> Optional[str]:
        """Download PDF for a paper and return file path."""
        pass

    @abstractmethod
    def get_available_sources(self) -> List[Dict[str, Any]]:
        """Get list of available paper sources."""
        pass


class IPublisherStrategy(ABC):
    """Interface for publisher-specific interaction strategies."""

    @property
    @abstractmethod
    def publisher_name(self) -> str:
        """Get publisher name."""
        pass

    @property
    @abstractmethod
    def interaction_type(self) -> PublisherInteractionType:
        """Get interaction type for this publisher."""
        pass

    @abstractmethod
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search papers using publisher-specific method."""
        pass

    @abstractmethod
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check if PDF is available for download."""
        pass

    @abstractmethod
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get direct PDF URL if available."""
        pass

    @abstractmethod
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limiting configuration for this publisher."""
        pass


class IPdfAvailabilityService(ABC):
    """Interface for PDF availability checking service."""

    @abstractmethod
    async def check_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for a single paper."""
        pass

    @abstractmethod
    async def check_batch_availability(self, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Check PDF availability for multiple papers."""
        pass

    @abstractmethod
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL if available."""
        pass


class IDownloadTaskRepository(ABC):
    """Interface for download task operations."""
    
    @abstractmethod
    def create_task(self, papers: List[Paper]) -> DownloadTask:
        """Create a new download task."""
        pass
    
    @abstractmethod
    def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get download task by ID."""
        pass
    
    @abstractmethod
    def update_task(self, task: DownloadTask) -> None:
        """Update download task."""
        pass
    
    @abstractmethod
    def get_active_tasks(self) -> List[DownloadTask]:
        """Get all active download tasks."""
        pass


class INotificationService(ABC):
    """Interface for sending real-time notifications."""
    
    @abstractmethod
    async def send_search_progress(self, query_id: str, progress: Dict[str, Any]) -> None:
        """Send search progress notification."""
        pass
    
    @abstractmethod
    async def send_download_progress(self, task_id: str, progress: Dict[str, Any]) -> None:
        """Send download progress notification."""
        pass
    
    @abstractmethod
    async def send_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        """Send task completion notification."""
        pass 
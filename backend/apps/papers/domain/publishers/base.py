"""
Ultra-High Performance Base Publisher Strategy.
Optimized for maximum speed with minimal overhead.
"""

import asyncio
import aiohttp
import time
import re
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..interfaces import IPublisherStrategy, PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class UltraFastBasePublisherStrategy(IPublisherStrategy):
    """Ultra-optimized base implementation for maximum performance."""

    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session
        self._session_owner = False

        # Ultra-fast rate limiter with minimal overhead
        self._rate_limiter = UltraFastRateLimiter(self.get_rate_limits())

        # Pre-compiled regex patterns for maximum speed
        self.doi_pattern = re.compile(r'10\.\d{4,}[^\s]*[^\s\.,]', re.IGNORECASE)
        self.whitespace_pattern = re.compile(r'\s+')

        # Pre-allocated data structures
        self._temp_authors = []
        self._temp_keywords = []
    
    async def __aenter__(self):
        if not self.session:
            self.session = aiohttp.ClientSession()
            self._session_owner = True
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and self._session_owner:
            await self.session.close()
    
    @property
    @abstractmethod
    def publisher_name(self) -> str:
        """Get publisher name."""
        pass
    
    @property
    @abstractmethod
    def interaction_type(self) -> PublisherInteractionType:
        """Get interaction type for this publisher."""
        pass
    
    @abstractmethod
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search papers using publisher-specific method."""
        pass
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Default PDF availability check implementation."""
        if not paper.pdf_url:
            return PdfAvailabilityStatus.UNAVAILABLE
        
        try:
            await self._rate_limiter.wait()
            async with self.session.head(paper.pdf_url, timeout=5) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    if 'application/pdf' in content_type:
                        return PdfAvailabilityStatus.AVAILABLE
                    return PdfAvailabilityStatus.UNAVAILABLE
                else:
                    return PdfAvailabilityStatus.UNAVAILABLE
        except Exception:
            return PdfAvailabilityStatus.ERROR
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Default PDF URL getter."""
        return paper.pdf_url
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Default rate limiting configuration."""
        return {
            'requests_per_second': 2,
            'burst_size': 5,
            'backoff_factor': 1.5
        }
    
    def extract_doi_fast(self, text: str) -> Optional[str]:
        """Ultra-fast DOI extraction using pre-compiled regex."""
        if not text:
            return None
        match = self.doi_pattern.search(text)
        return match.group(0) if match else None

    def clean_title_fast(self, title: str) -> str:
        """Ultra-fast title cleaning with minimal string operations."""
        if not title:
            return ""

        # Single-pass whitespace normalization
        title = self.whitespace_pattern.sub(' ', title).strip()

        # Fast prefix removal using string slicing
        if title.startswith(('Abstract:', 'Title:', 'Paper:')):
            colon_pos = title.find(':')
            if colon_pos != -1:
                title = title[colon_pos + 1:].strip()

        return title

    def parse_authors_fast(self, authors_data: Any) -> List[str]:
        """Ultra-fast author parsing with pre-allocated lists."""
        if not authors_data:
            return []

        # Clear and reuse pre-allocated list for performance
        self._temp_authors.clear()

        if isinstance(authors_data, str):
            # Fast string splitting
            for author in authors_data.split(','):
                author = author.strip()
                if author:
                    self._temp_authors.append(author)
        elif isinstance(authors_data, list):
            for author in authors_data:
                if isinstance(author, str):
                    author = author.strip()
                    if author:
                        self._temp_authors.append(author)
                elif isinstance(author, dict):
                    # Fast dict access with fallbacks
                    name = author.get('name')
                    if not name:
                        given = author.get('given', '')
                        family = author.get('family', '')
                        name = f"{given} {family}".strip()
                    if name:
                        self._temp_authors.append(name)

        # Return copy to avoid mutation
        return self._temp_authors.copy()
    
    def parse_date_fast(self, date_str: str) -> Optional[datetime]:
        """Ultra-fast date parsing with optimized format checking."""
        if not date_str:
            return None

        date_str = date_str.strip()

        # Fast path for most common ISO format
        if len(date_str) >= 10 and date_str[4] == '-' and date_str[7] == '-':
            try:
                if 'T' in date_str:
                    # ISO datetime format
                    if date_str.endswith('Z'):
                        return datetime.fromisoformat(date_str[:-1])
                    else:
                        return datetime.fromisoformat(date_str.split('+')[0].split('.')[0])
                else:
                    # ISO date format
                    return datetime.fromisoformat(date_str)
            except ValueError:
                pass

        # Fast path for year-only
        if len(date_str) == 4 and date_str.isdigit():
            try:
                return datetime(int(date_str), 1, 1)
            except ValueError:
                pass

        return None


class UltraFastRateLimiter:
    """Ultra-optimized rate limiter with minimal overhead."""

    def __init__(self, config: Dict[str, Any]):
        self.requests_per_second = config.get('requests_per_second', 1)
        self.min_interval = 1.0 / self.requests_per_second if self.requests_per_second > 0 else 0
        self.last_request_time = 0.0

    async def wait(self):
        """Ultra-fast rate limiting with minimal overhead."""
        if self.min_interval <= 0:
            return

        current_time = time.perf_counter()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_interval:
            sleep_time = self.min_interval - time_since_last
            await asyncio.sleep(sleep_time)
            self.last_request_time = time.perf_counter()
        else:
            self.last_request_time = current_time


# Update the base class name for consistency
BasePublisherStrategy = UltraFastBasePublisherStrategy

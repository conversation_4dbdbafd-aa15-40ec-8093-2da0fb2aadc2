"""
Sci-Hub publisher strategy implementation.
Handles Sci-Hub PDF availability checking and download.
"""

from typing import List, Optional
from bs4 import BeautifulSoup

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class SciHubPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for Sci-Hub PDF access."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_urls = [
            'https://sci-hub.se/',
            'https://sci-hub.st/', 
            'https://sci-hub.ru/',
            'https://sci-hub.wf/',
        ]
    
    @property
    def publisher_name(self) -> str:
        return "scihub"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.WEB_SCRAPING
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Sci-Hub doesn't provide search functionality."""
        return []
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check if PDF is available on Sci-Hub."""
        if not paper.doi:
            return PdfAvailabilityStatus.UNAVAILABLE
        
        pdf_url = await self.get_pdf_url(paper)
        if pdf_url:
            paper.pdf_url = pdf_url
            return PdfAvailabilityStatus.AVAILABLE
        
        return PdfAvailabilityStatus.UNAVAILABLE
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL from Sci-Hub for a given DOI."""
        if not paper.doi:
            return None
        
        for base_url in self.base_urls:
            try:
                await self._rate_limiter.wait()
                
                url = f"{base_url}{paper.doi}"
                async with self.session.get(
                    url, 
                    allow_redirects=True,
                    timeout=10
                ) as response:
                    if response.status == 200:
                        content = await response.text()
                        pdf_url = self._extract_pdf_url(content, base_url)
                        if pdf_url:
                            return pdf_url
                            
            except Exception as e:
                print(f"Error accessing Sci-Hub {base_url}: {e}")
                continue
                
        return None
    
    def get_rate_limits(self) -> dict:
        """Sci-Hub rate limiting configuration."""
        return {
            'requests_per_second': 0.5,  # Very conservative for Sci-Hub
            'burst_size': 2,
            'backoff_factor': 3.0  # Aggressive backoff
        }
    
    def _extract_pdf_url(self, html_content: str, base_url: str) -> Optional[str]:
        """Extract PDF URL from Sci-Hub page."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for PDF embed
            pdf_embed = soup.find('embed', {'type': 'application/pdf'})
            if pdf_embed and pdf_embed.get('src'):
                pdf_url = pdf_embed['src']
                return self._normalize_url(pdf_url, base_url)
            
            # Look for iframe with PDF
            pdf_iframe = soup.find('iframe', {'id': 'pdf'})
            if pdf_iframe and pdf_iframe.get('src'):
                pdf_url = pdf_iframe['src']
                return self._normalize_url(pdf_url, base_url)
            
            # Look for direct PDF links
            pdf_links = soup.find_all('a', href=True)
            for link in pdf_links:
                href = link['href']
                if href.endswith('.pdf') or 'pdf' in href.lower():
                    return self._normalize_url(href, base_url)
            
            # Look for button with PDF download
            download_button = soup.find('button', {'onclick': True})
            if download_button:
                onclick = download_button.get('onclick', '')
                if 'pdf' in onclick.lower():
                    # Extract URL from onclick JavaScript
                    import re
                    url_match = re.search(r"location\.href='([^']+)'", onclick)
                    if url_match:
                        return self._normalize_url(url_match.group(1), base_url)
                        
        except Exception as e:
            print(f"Error extracting PDF URL from Sci-Hub: {e}")
        
        return None
    
    def _normalize_url(self, url: str, base_url: str) -> str:
        """Normalize URL to absolute format."""
        if url.startswith('//'):
            return f"https:{url}"
        elif url.startswith('/'):
            return f"{base_url.rstrip('/')}{url}"
        elif url.startswith('http'):
            return url
        else:
            return f"{base_url.rstrip('/')}/{url}"

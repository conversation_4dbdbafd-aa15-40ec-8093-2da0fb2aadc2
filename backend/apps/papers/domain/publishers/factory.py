"""
Publisher strategy factory for creating appropriate publisher strategies.
Implements the Factory pattern for clean publisher strategy instantiation.
"""

import aiohttp
from typing import Optional, Dict, Type

from ..interfaces import IPublisherStrategy
from .base import BasePublisherStrategy
from .arxiv import ArxivPublisherStrategy
from .crossref import CrossrefPublisherStrategy
from .asm import ASMPublisherStrategy
from .scihub import SciHubPublisherStrategy


class PublisherStrategyFactory:
    """Factory for creating publisher strategies."""
    
    _strategies: Dict[str, Type[IPublisherStrategy]] = {
        'arxiv': ArxivPublisherStrategy,
        'crossref': CrossrefPublisherStrategy,
        'asm': ASMPublisherStrategy,
        'scihub': SciHubPublisherStrategy,
    }
    
    @classmethod
    def create_strategy(
        self, 
        publisher: str, 
        session: Optional[aiohttp.ClientSession] = None
    ) -> IPublisherStrategy:
        """
        Create appropriate publisher strategy.
        
        Args:
            publisher: Publisher name (e.g., 'arxiv', 'crossref', 'asm', 'scihub')
            session: Optional aiohttp session to reuse
            
        Returns:
            Publisher strategy instance
            
        Raises:
            ValueError: If publisher is not supported
        """
        publisher = publisher.lower()
        
        if publisher not in self._strategies:
            raise ValueError(f"Unsupported publisher: {publisher}")
        
        strategy_class = self._strategies[publisher]
        return strategy_class(session=session)
    
    @classmethod
    def get_supported_publishers(cls) -> list[str]:
        """Get list of supported publisher names."""
        return list(cls._strategies.keys())
    
    @classmethod
    def register_strategy(
        cls, 
        publisher: str, 
        strategy_class: Type[IPublisherStrategy]
    ) -> None:
        """
        Register a new publisher strategy.
        
        Args:
            publisher: Publisher name
            strategy_class: Strategy class implementing IPublisherStrategy
        """
        cls._strategies[publisher.lower()] = strategy_class
    
    @classmethod
    def is_supported(cls, publisher: str) -> bool:
        """Check if publisher is supported."""
        return publisher.lower() in cls._strategies

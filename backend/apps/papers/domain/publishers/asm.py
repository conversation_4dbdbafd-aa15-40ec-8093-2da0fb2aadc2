"""
ASM (American Society for Microbiology) publisher strategy implementation.
Handles ASM-specific search and PDF availability checking.
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from bs4 import BeautifulSoup

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class ASMPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for ASM journals."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_url = "https://journals.asm.org"
        self.search_url = "https://journals.asm.org/action/doSearch"
        self.crossref_url = "https://api.crossref.org/works"
        
        # ASM journal list for targeted searches
        self.asm_journals = [
            "Journal of Bacteriology",
            "Applied and Environmental Microbiology", 
            "Antimicrobial Agents and Chemotherapy",
            "Journal of Virology",
            "Infection and Immunity",
            "Journal of Clinical Microbiology",
            "mBio",
            "Microbiology Spectrum"
        ]
    
    @property
    def publisher_name(self) -> str:
        return "asm"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.HYBRID  # Uses both API and web scraping
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search ASM papers using hybrid approach."""
        papers = []
        
        # Run multiple search strategies in parallel
        search_tasks = [
            self._search_via_crossref(query),
            self._search_direct_asm(query),
            self._search_asm_rss(query)
        ]
        
        try:
            results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # Combine results and deduplicate
            seen_dois = set()
            for result in results:
                if isinstance(result, list):
                    for paper in result:
                        if paper.doi and paper.doi not in seen_dois:
                            papers.append(paper)
                            seen_dois.add(paper.doi)
                        elif not paper.doi:
                            papers.append(paper)
                            
        except Exception as e:
            print(f"Error in ASM search: {e}")
        
        return papers[:query.max_results]
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for ASM papers."""
        if not paper.pdf_url and paper.doi:
            # Try to construct ASM PDF URL
            pdf_url = await self._get_asm_pdf_url(paper)
            if pdf_url:
                paper.pdf_url = pdf_url
        
        if paper.pdf_url:
            return await super().check_pdf_availability(paper)
        
        return PdfAvailabilityStatus.UNAVAILABLE
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for ASM paper."""
        if paper.pdf_url:
            return paper.pdf_url
        
        return await self._get_asm_pdf_url(paper)
    
    def get_rate_limits(self) -> dict:
        """ASM rate limiting configuration."""
        return {
            'requests_per_second': 1,  # Conservative for ASM
            'burst_size': 3,
            'backoff_factor': 2.0  # Aggressive backoff
        }
    
    async def _search_via_crossref(self, query: SearchQuery) -> List[Paper]:
        """Search ASM papers via CrossRef API."""
        papers = []
        
        try:
            # Search each ASM journal separately for better results
            for journal in self.asm_journals[:3]:  # Limit to top journals for speed
                await self._rate_limiter.wait()
                
                params = {
                    'query': query.query,
                    'filter': f'container-title:{journal}',
                    'rows': max(3, query.max_results // len(self.asm_journals)),
                    'mailto': '<EMAIL>',
                    'sort': 'relevance'
                }
                
                async with self.session.get(
                    self.crossref_url, 
                    params=params,
                    timeout=5
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        journal_papers = self._parse_crossref_asm_response(data)
                        papers.extend(journal_papers)
                        
        except Exception as e:
            print(f"Error in CrossRef ASM search: {e}")
        
        return papers
    
    async def _search_direct_asm(self, query: SearchQuery) -> List[Paper]:
        """Search ASM directly via their website."""
        papers = []
        
        try:
            await self._rate_limiter.wait()
            
            # Use simple search to avoid Cloudflare issues
            params = {
                'AllField': query.query,
                'content': 'articlesChapters',
                'target': 'default',
                'startPage': '0',
                'ContentItemType': 'Journals'
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            async with self.session.get(
                self.search_url, 
                params=params, 
                headers=headers,
                timeout=10
            ) as response:
                if response.status == 200:
                    content = await response.text()
                    papers = self._parse_asm_response(content)
                elif response.status == 403:
                    print("ASM search blocked - trying alternative approach")
                    
        except Exception as e:
            print(f"Error in direct ASM search: {e}")
        
        return papers
    
    async def _search_asm_rss(self, query: SearchQuery) -> List[Paper]:
        """Search ASM via RSS feeds (fallback method)."""
        papers = []
        
        try:
            # This would implement RSS feed parsing for ASM journals
            # For now, return empty list as fallback
            pass
            
        except Exception as e:
            print(f"Error in ASM RSS search: {e}")
        
        return papers
    
    async def _get_asm_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for ASM paper."""
        if not paper.doi:
            return None
        
        try:
            # ASM PDF URLs typically follow pattern:
            # https://journals.asm.org/doi/pdf/10.1128/...
            if paper.doi.startswith('10.1128/'):
                return f"https://journals.asm.org/doi/pdf/{paper.doi}"
            
            # Try to get from paper landing page
            if paper.url and 'journals.asm.org' in paper.url:
                await self._rate_limiter.wait()
                
                async with self.session.get(paper.url, timeout=5) as response:
                    if response.status == 200:
                        content = await response.text()
                        soup = BeautifulSoup(content, 'html.parser')
                        
                        # Look for PDF download link
                        pdf_link = soup.find('a', {'class': 'pdf-download'})
                        if pdf_link and pdf_link.get('href'):
                            href = pdf_link['href']
                            if href.startswith('/'):
                                return f"{self.base_url}{href}"
                            return href
                            
        except Exception as e:
            print(f"Error getting ASM PDF URL: {e}")
        
        return None
    
    def _parse_crossref_asm_response(self, data: Dict[str, Any]) -> List[Paper]:
        """Parse CrossRef response for ASM papers."""
        papers = []
        
        try:
            message = data.get('message', {})
            items = message.get('items', [])
            
            for item in items:
                try:
                    paper = self._parse_crossref_asm_item(item)
                    if paper:
                        papers.append(paper)
                except Exception as e:
                    print(f"Error parsing CrossRef ASM item: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing CrossRef ASM response: {e}")
        
        return papers
    
    def _parse_crossref_asm_item(self, item: Dict[str, Any]) -> Optional[Paper]:
        """Parse individual CrossRef ASM item."""
        try:
            # Extract title
            titles = item.get('title', [])
            title = self.clean_title(titles[0]) if titles else ""
            
            if not title:
                return None
            
            # Extract DOI
            doi = item.get('DOI', '')
            
            # Extract URL - construct ASM URL from DOI
            url = f"https://journals.asm.org/doi/{doi}" if doi else item.get('URL', '')
            
            # Extract abstract
            abstract = item.get('abstract', '')
            
            # Extract authors
            authors = self.parse_authors(item.get('author', []))
            
            # Extract publication date
            publication_date = None
            date_parts = item.get('published-print', {}).get('date-parts')
            if not date_parts:
                date_parts = item.get('published-online', {}).get('date-parts')
            
            if date_parts and len(date_parts) > 0 and len(date_parts[0]) >= 3:
                year, month, day = date_parts[0][:3]
                try:
                    publication_date = datetime(year, month, day)
                except ValueError:
                    pass
            
            # Extract journal
            journal = ""
            container_titles = item.get('container-title', [])
            if container_titles:
                journal = container_titles[0]
            
            # Construct PDF URL for ASM
            pdf_url = None
            if doi and doi.startswith('10.1128/'):
                pdf_url = f"https://journals.asm.org/doi/pdf/{doi}"
            
            return Paper(
                title=title,
                doi=doi,
                url=url,
                pdf_url=pdf_url,
                abstract=abstract,
                authors=authors,
                publication_date=publication_date,
                journal=journal,
                source="asm"
            )
            
        except Exception as e:
            print(f"Error parsing CrossRef ASM item: {e}")
            return None
    
    def _parse_asm_response(self, html_content: str) -> List[Paper]:
        """Parse ASM website search response."""
        papers = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for search result items
            result_items = soup.find_all('div', class_='search-result-item')
            
            for item in result_items:
                try:
                    paper = self._parse_asm_search_item(item)
                    if paper:
                        papers.append(paper)
                except Exception as e:
                    print(f"Error parsing ASM search item: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing ASM response: {e}")
        
        return papers
    
    def _parse_asm_search_item(self, item) -> Optional[Paper]:
        """Parse individual ASM search result item."""
        try:
            # Extract title
            title_elem = item.find('h3') or item.find('a', class_='title')
            title = self.clean_title(title_elem.get_text()) if title_elem else ""
            
            if not title:
                return None
            
            # Extract URL
            url = ""
            if title_elem and title_elem.find('a'):
                href = title_elem.find('a').get('href', '')
                if href.startswith('/'):
                    url = f"{self.base_url}{href}"
                else:
                    url = href
            
            # Extract DOI from URL or text
            doi = self.extract_doi(str(item))
            
            # Extract authors
            authors_elem = item.find('div', class_='authors')
            authors = []
            if authors_elem:
                authors_text = authors_elem.get_text()
                authors = self.parse_authors(authors_text)
            
            # Extract journal
            journal_elem = item.find('div', class_='journal')
            journal = journal_elem.get_text().strip() if journal_elem else ""
            
            # Construct PDF URL if we have DOI
            pdf_url = None
            if doi and doi.startswith('10.1128/'):
                pdf_url = f"https://journals.asm.org/doi/pdf/{doi}"
            
            return Paper(
                title=title,
                doi=doi,
                url=url,
                pdf_url=pdf_url,
                authors=authors,
                journal=journal,
                source="asm"
            )
            
        except Exception as e:
            print(f"Error parsing ASM search item: {e}")
            return None

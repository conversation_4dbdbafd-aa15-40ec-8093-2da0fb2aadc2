"""
CrossRef publisher strategy implementation.
Handles CrossRef API interactions and PDF availability checking.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class CrossrefPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for CrossRef API."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_url = "https://api.crossref.org/works"
        self.headers = {
            'User-Agent': 'PaperDownloader/1.0 (mailto:<EMAIL>)'
        }
    
    @property
    def publisher_name(self) -> str:
        return "crossref"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.API
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Ultra-fast CrossRef search with optimized parameters."""
        try:
            await self._rate_limiter.wait()

            # Optimized parameters for maximum speed
            params = {
                'query': query.query,
                'rows': min(query.max_results, 50),  # Reduced for faster response
                'sort': 'relevance',
                'mailto': '<EMAIL>',
                'select': 'DOI,title,author,abstract,published-print,published-online,container-title,subject,link'  # Only fetch needed fields
            }

            async with self.session.get(
                self.base_url,
                params=params,
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_crossref_response_fast(data)

        except Exception:
            pass  # Silent failure for speed

        return []
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for CrossRef papers."""
        # CrossRef doesn't host PDFs directly, but provides links
        if not paper.pdf_url and not paper.doi:
            return PdfAvailabilityStatus.UNAVAILABLE
        
        # Try to get PDF URL from publisher
        pdf_url = await self._get_publisher_pdf_url(paper)
        if pdf_url:
            paper.pdf_url = pdf_url
            return await super().check_pdf_availability(paper)
        
        return PdfAvailabilityStatus.UNAVAILABLE
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for CrossRef paper."""
        if paper.pdf_url:
            return paper.pdf_url
        
        return await self._get_publisher_pdf_url(paper)
    
    def get_rate_limits(self) -> dict:
        """CrossRef rate limiting configuration."""
        return {
            'requests_per_second': 50,  # CrossRef is quite generous
            'burst_size': 100,
            'backoff_factor': 1.0
        }
    
    async def _get_publisher_pdf_url(self, paper: Paper) -> Optional[str]:
        """Try to get PDF URL from publisher via CrossRef metadata."""
        if not paper.doi:
            return None
        
        try:
            await self._rate_limiter.wait()
            
            # Get detailed metadata for this DOI
            url = f"{self.base_url}/{paper.doi}"
            async with self.session.get(url, headers=self.headers) as response:
                if response.status == 200:
                    data = await response.json()
                    work = data.get('message', {})
                    
                    # Look for PDF links
                    links = work.get('link', [])
                    for link in links:
                        if link.get('content-type') == 'application/pdf':
                            return link.get('URL')
                    
                    # Look for full-text links
                    resource = work.get('resource', {})
                    primary = resource.get('primary', {})
                    if primary.get('URL'):
                        # This might be a landing page, but could lead to PDF
                        return primary.get('URL')
                        
        except Exception as e:
            print(f"Error getting publisher PDF URL: {e}")
        
        return None
    
    def _parse_crossref_response_fast(self, data: Dict[str, Any]) -> List[Paper]:
        """Ultra-fast CrossRef response parsing."""
        papers = []

        try:
            # Direct access for speed
            items = data.get('message', {}).get('items', [])

            for item in items:
                try:
                    paper = self._parse_crossref_item_fast(item)
                    if paper:
                        papers.append(paper)
                except Exception:
                    continue  # Skip errors silently for speed

        except Exception:
            pass  # Silent failure for speed

        return papers
    
    def _parse_crossref_item_fast(self, item: Dict[str, Any]) -> Optional[Paper]:
        """Ultra-fast CrossRef item parsing with minimal overhead."""
        try:
            # Fast title extraction
            titles = item.get('title')
            if not titles:
                return None
            title = self.clean_title_fast(titles[0])
            if not title:
                return None

            # Fast field extraction
            doi = item.get('DOI', '')
            url = item.get('URL', '')
            abstract = item.get('abstract', '')

            # Fast author extraction
            self._temp_authors.clear()
            author_list = item.get('author', [])
            for author in author_list:
                given = author.get('given', '')
                family = author.get('family', '')
                if given or family:
                    name = f"{given} {family}".strip()
                    if name:
                        self._temp_authors.append(name)
            authors = self._temp_authors.copy()

            # Fast date extraction
            publication_date = None
            date_parts = item.get('published-print', {}).get('date-parts')
            if not date_parts:
                date_parts = item.get('published-online', {}).get('date-parts')

            if date_parts and date_parts[0] and len(date_parts[0]) >= 1:
                try:
                    year = date_parts[0][0]
                    month = date_parts[0][1] if len(date_parts[0]) > 1 else 1
                    day = date_parts[0][2] if len(date_parts[0]) > 2 else 1
                    publication_date = datetime(year, month, day)
                except (ValueError, IndexError):
                    pass

            # Fast journal extraction
            container_titles = item.get('container-title', [])
            journal = container_titles[0] if container_titles else ""

            # Fast keywords extraction
            keywords = item.get('subject', [])

            # Fast PDF URL extraction
            pdf_url = None
            links = item.get('link', [])
            for link in links:
                if link.get('content-type') == 'application/pdf':
                    pdf_url = link.get('URL')
                    break

            # Fast Paper object creation
            paper = Paper.__new__(Paper)
            paper.title = title
            paper.doi = doi
            paper.url = url
            paper.pdf_url = pdf_url
            paper.abstract = abstract
            paper.authors = authors
            paper.publication_date = publication_date
            paper.journal = journal
            paper.keywords = keywords
            paper.source = "crossref"

            return paper

        except Exception:
            return None

"""
ArXiv publisher strategy implementation.
Handles ArXiv-specific search and PDF availability checking.
"""

import xml.etree.ElementTree as ET
from typing import List, Optional
from datetime import datetime

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class ArxivPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for ArXiv papers."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_url = "http://export.arxiv.org/api/query"
        self.pdf_base_url = "https://arxiv.org/pdf/"
    
    @property
    def publisher_name(self) -> str:
        return "arxiv"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.XML_FEED
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Ultra-fast ArXiv search with optimized parsing."""
        try:
            await self._rate_limiter.wait()

            # Optimized query parameters for maximum speed
            params = {
                'search_query': f'all:{query.query}',
                'start': 0,
                'max_results': min(query.max_results, 50),  # Reduced for faster response
                'sortBy': 'relevance',
                'sortOrder': 'descending'
            }

            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    # Stream content for faster processing
                    content = await response.text()
                    return self._parse_arxiv_response_fast(content)

        except Exception:
            pass  # Silent failure for speed

        return []
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for ArXiv papers."""
        if not paper.pdf_url:
            # Try to construct ArXiv PDF URL from paper ID
            arxiv_id = self._extract_arxiv_id(paper)
            if arxiv_id:
                paper.pdf_url = f"{self.pdf_base_url}{arxiv_id}.pdf"
            else:
                return PdfAvailabilityStatus.UNAVAILABLE
        
        # ArXiv PDFs are generally always available
        try:
            await self._rate_limiter.wait()
            async with self.session.head(paper.pdf_url, timeout=5) as response:
                if response.status == 200:
                    return PdfAvailabilityStatus.AVAILABLE
                else:
                    return PdfAvailabilityStatus.UNAVAILABLE
        except Exception:
            return PdfAvailabilityStatus.ERROR
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for ArXiv paper."""
        if paper.pdf_url:
            return paper.pdf_url
        
        # Try to construct from ArXiv ID
        arxiv_id = self._extract_arxiv_id(paper)
        if arxiv_id:
            return f"{self.pdf_base_url}{arxiv_id}.pdf"
        
        return None
    
    def get_rate_limits(self) -> dict:
        """ArXiv rate limiting configuration."""
        return {
            'requests_per_second': 3,  # ArXiv allows up to 3 requests per second
            'burst_size': 5,
            'backoff_factor': 1.0
        }
    
    def _parse_arxiv_response_fast(self, xml_content: str) -> List[Paper]:
        """Ultra-fast ArXiv XML parsing with minimal overhead."""
        papers = []

        try:
            root = ET.fromstring(xml_content)

            # Pre-defined namespaces for speed
            atom_ns = '{http://www.w3.org/2005/Atom}'
            arxiv_ns = '{http://arxiv.org/schemas/atom}'

            # Direct element access for maximum speed
            for entry in root.iter(f'{atom_ns}entry'):
                try:
                    paper = self._parse_arxiv_entry_fast(entry, atom_ns, arxiv_ns)
                    if paper:
                        papers.append(paper)
                except Exception:
                    continue  # Skip errors silently for speed

        except Exception:
            pass  # Silent failure for speed

        return papers
    
    def _parse_arxiv_entry_fast(self, entry, atom_ns: str, arxiv_ns: str) -> Optional[Paper]:
        """Ultra-fast ArXiv entry parsing with direct element access."""
        try:
            # Fast title extraction
            title_elem = entry.find(f'{atom_ns}title')
            if title_elem is None or not title_elem.text:
                return None
            title = self.clean_title_fast(title_elem.text)
            if not title:
                return None

            # Fast ArXiv ID extraction
            id_elem = entry.find(f'{atom_ns}id')
            arxiv_url = id_elem.text if id_elem is not None else ""
            arxiv_id = arxiv_url.split('/')[-1] if arxiv_url else ""

            # Fast abstract extraction
            summary_elem = entry.find(f'{atom_ns}summary')
            abstract = summary_elem.text.strip() if summary_elem is not None and summary_elem.text else ""

            # Fast author extraction with pre-allocated list
            self._temp_authors.clear()
            for author_elem in entry.iter(f'{atom_ns}author'):
                name_elem = author_elem.find(f'{atom_ns}name')
                if name_elem is not None and name_elem.text:
                    self._temp_authors.append(name_elem.text.strip())
            authors = self._temp_authors.copy()

            # Fast date extraction
            published_elem = entry.find(f'{atom_ns}published')
            publication_date = None
            if published_elem is not None and published_elem.text:
                publication_date = self.parse_date_fast(published_elem.text)

            # Fast keyword extraction with pre-allocated list
            self._temp_keywords.clear()
            for cat_elem in entry.iter(f'{atom_ns}category'):
                term = cat_elem.get('term')
                if term:
                    self._temp_keywords.append(term)
            keywords = self._temp_keywords.copy()

            # Fast PDF URL construction
            pdf_url = f"{self.pdf_base_url}{arxiv_id}.pdf" if arxiv_id else None

            # Fast DOI extraction
            doi = None
            doi_elem = entry.find(f'{arxiv_ns}doi')
            if doi_elem is not None and doi_elem.text:
                doi = doi_elem.text.strip()

            # Fast Paper object creation
            paper = Paper.__new__(Paper)
            paper.title = title
            paper.doi = doi
            paper.url = arxiv_url
            paper.pdf_url = pdf_url
            paper.abstract = abstract
            paper.authors = authors
            paper.publication_date = publication_date
            paper.keywords = keywords
            paper.source = "arxiv"

            return paper

        except Exception:
            return None
    
    def _extract_arxiv_id(self, paper: Paper) -> Optional[str]:
        """Extract ArXiv ID from paper URL or other fields."""
        if paper.url and 'arxiv.org' in paper.url:
            # Extract from URL like https://arxiv.org/abs/2301.12345
            parts = paper.url.split('/')
            if len(parts) > 0:
                return parts[-1]
        
        # Could also check DOI or other fields for ArXiv ID
        return None

"""
Publisher abstraction layer for paper downloader.
This module provides clean separation of concerns for different publisher interactions.
"""

from .base import BasePublisherStrategy
from .arxiv import ArxivPublisherStrategy
from .crossref import CrossrefPublisherStrategy
from .asm import ASMPublisherStrategy
from .scihub import SciHubPublisherStrategy
from .factory import PublisherStrategyFactory

__all__ = [
    'BasePublisherStrategy',
    'ArxivPublisherStrategy', 
    'CrossrefPublisherStrategy',
    'ASMPublisherStrategy',
    'SciHubPublisherStrategy',
    'PublisherStrategyFactory'
]

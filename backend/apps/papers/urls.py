"""
URL configuration for the papers app.
"""

from django.urls import path
from .presentation import views

app_name = 'papers'

urlpatterns = [
    # Health check
    path('health/', views.health_check, name='health'),
    
    # Paper search and sources
    path('papers/search/', views.search_papers, name='search_papers'),
    path('papers/sources/', views.get_available_sources, name='available_sources'),
    
    # Download tasks
    path('papers/download/', views.create_download_task, name='create_download_task'),
    path('papers/download/single/', views.download_single_paper, name='download_single_paper'),
    path('papers/download/active/', views.get_active_downloads, name='get_active_downloads'),
    path('papers/download/tasks/', views.get_all_download_tasks, name='get_all_download_tasks'),
    path('papers/download/<str:task_id>/status/', views.get_download_task_status, name='download_task_status'),

    # PDF availability checking
    path('papers/pdf-availability/check/', views.check_pdf_availability, name='check_pdf_availability'),
    path('papers/pdf-availability/batch/', views.check_batch_pdf_availability, name='check_batch_pdf_availability'),
] 
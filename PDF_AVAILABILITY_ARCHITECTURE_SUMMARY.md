# PDF Availability & Publisher Abstraction Architecture

## Overview

This document summarizes the implementation of a clean separation of concerns architecture for PDF availability checking and publisher interactions, following MVVM principles.

## Architecture Changes

### 1. Publisher Abstraction Layer

**Location**: `backend/apps/papers/domain/publishers/`

**Components**:
- `base.py` - Base publisher strategy with common functionality
- `arxiv.py` - ArXiv-specific implementation (XML API)
- `crossref.py` - CrossRef API implementation
- `asm.py` - ASM journals hybrid approach (API + web scraping)
- `scihub.py` - Sci-Hub web scraping implementation
- `factory.py` - Factory pattern for creating publisher strategies

**Key Features**:
- Each publisher has its own interaction strategy (API, XML, web scraping, hybrid)
- Rate limiting per publisher with configurable settings
- Clean separation of publisher-specific logic
- Extensible design for adding new publishers

### 2. PDF Availability Service

**Location**: `backend/apps/papers/services/pdf_availability.py`

**Features**:
- Asynchronous PDF availability checking
- Batch processing for multiple papers
- Intelligent caching with different timeouts based on availability status
- Publisher-agnostic interface
- Error handling and fallback strategies

### 3. Search Orchestrator

**Location**: `backend/apps/papers/data/search_orchestrator.py`

**Features**:
- Coordinates search across multiple publishers
- Uses publisher strategies for clean separation
- Concurrent search execution
- Intelligent deduplication
- Proper error handling and timeout management

### 4. Frontend PDF Availability System

**Components**:
- `frontend/lib/services/pdf-availability.ts` - Service layer for PDF availability
- `frontend/lib/hooks/usePdfAvailability.ts` - React hooks for state management
- Updated search results component with real-time PDF status

**Features**:
- Reactive PDF availability checking
- Batch processing with intelligent queuing
- Caching with automatic invalidation
- Real-time UI updates
- Optimistic UI patterns

## API Endpoints

### New Endpoints Added:

1. **Single PDF Availability Check**
   ```
   POST /api/v1/papers/pdf-availability/check/
   ```

2. **Batch PDF Availability Check**
   ```
   POST /api/v1/papers/pdf-availability/batch/
   ```

## Key Benefits

### 1. Separation of Concerns
- Publisher logic is isolated in strategy classes
- PDF availability checking is separate from search logic
- Clear boundaries between domain, service, and presentation layers

### 2. Performance Improvements
- Asynchronous PDF checking doesn't block search results
- Batch processing reduces API calls
- Intelligent caching reduces redundant checks
- Real-time UI updates improve user experience

### 3. Extensibility
- Easy to add new publishers by implementing `IPublisherStrategy`
- Publisher-specific configurations (rate limits, interaction types)
- Pluggable architecture for different PDF sources

### 4. MVVM Implementation
- **Models**: Domain objects (`Paper`, `SearchQuery`) and DTOs
- **Views**: React components with reactive state
- **ViewModels**: React hooks and services managing state and business logic

## Publisher Interaction Types

### API-based Publishers
- **CrossRef**: REST API with JSON responses
- **ArXiv**: XML API with structured data

### Web Scraping Publishers
- **Sci-Hub**: HTML parsing for PDF extraction

### Hybrid Publishers
- **ASM**: Combines CrossRef API + direct website scraping + RSS feeds

## PDF Availability States

```typescript
enum PdfAvailabilityStatus {
  UNKNOWN = 'unknown',      // Not yet checked
  CHECKING = 'checking',    // Currently being checked
  AVAILABLE = 'available',  // PDF is available for download
  UNAVAILABLE = 'unavailable', // PDF is not available
  ERROR = 'error'          // Error occurred during check
}
```

## User Experience Flow

1. **Search Results Display**: Papers are shown immediately with unknown PDF status
2. **Async PDF Checking**: PDF availability is checked in the background
3. **Real-time Updates**: UI updates as PDF status becomes available
4. **Smart Button States**: Download/view buttons are enabled/disabled based on availability
5. **Visual Indicators**: Icons show PDF availability status

## Rate Limiting Strategy

Each publisher has configurable rate limits:
- **ArXiv**: 3 requests/second (generous, as per their guidelines)
- **CrossRef**: 50 requests/second (very generous API)
- **ASM**: 1 request/second (conservative for website scraping)
- **Sci-Hub**: 0.5 requests/second (very conservative)

## Caching Strategy

- **Available PDFs**: Cached for 24 hours
- **Unavailable PDFs**: Cached for 30 minutes
- **Error states**: Cached for 5 minutes
- **Client-side cache**: 5 minutes with automatic invalidation

## Error Handling

- Graceful degradation when publishers are unavailable
- Fallback strategies for PDF availability checking
- User-friendly error messages
- Automatic retry mechanisms with exponential backoff

## Future Enhancements

1. **Publisher Health Monitoring**: Track publisher availability and response times
2. **Smart Publisher Selection**: Choose best publishers based on query type
3. **PDF Preview**: Generate thumbnails for available PDFs
4. **Offline PDF Management**: Local PDF storage and indexing
5. **Publisher Analytics**: Track success rates and performance metrics

## Testing Strategy

- Unit tests for each publisher strategy
- Integration tests for PDF availability service
- End-to-end tests for the complete flow
- Performance tests for batch operations
- Mock services for reliable testing

This architecture provides a solid foundation for scalable, maintainable, and performant PDF availability checking while maintaining clean separation of concerns and following MVVM principles.
